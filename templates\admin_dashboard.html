{% extends "base.html" %}

{% block title %}Admin Dashboard - Attendance System{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Header -->
    <div class="dashboard-header">
        <div>
            <h1><i class="fas fa-tachometer-alt"></i> Admin Dashboard</h1>
            <p>Welcome, {{ session.full_name }}</p>
        </div>
        <a href="{{ url_for('logout') }}" class="btn btn-danger">
            <i class="fas fa-sign-out-alt"></i> Logout
        </a>
    </div>

    <!-- Statistics -->
    <div class="stats-grid">
        <div class="stat-card">
            <i class="fas fa-users"></i>
            <h3>{{ stats.total_users }}</h3>
            <p>Total Users</p>
        </div>
        <div class="stat-card">
            <i class="fas fa-user-shield"></i>
            <h3>{{ stats.total_admins }}</h3>
            <p>Administrators</p>
        </div>
        <div class="stat-card">
            <i class="fas fa-calendar-check"></i>
            <h3>{{ stats.today_attendance }}</h3>
            <p>Today's Attendance</p>
        </div>
        <div class="stat-card">
            <i class="fas fa-chart-line"></i>
            <h3>{{ stats.total_records }}</h3>
            <p>Total Records</p>
        </div>
    </div>

    <!-- User Management Section -->
    <div class="dashboard-section">
        <div class="section-header">
            <h2><i class="fas fa-users-cog"></i> User Management</h2>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addUserModal">
                <i class="fas fa-plus"></i> Add User
            </button>
        </div>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Username</th>
                        <th>Full Name</th>
                        <th>Email</th>
                        <th>Role</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>{{ user.id }}</td>
                        <td>{{ user.username }}</td>
                        <td>{{ user.full_name }}</td>
                        <td>{{ user.email }}</td>
                        <td>
                            <span class="badge bg-{{ 'danger' if user.role == 'admin' else 'success' }}">
                                {{ user.role.title() }}
                            </span>
                        </td>
                        <td>{{ user.created_at[:10] }}</td>
                        <td>
                            {% if user.id != 1 %}
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteUser({{ user.id }})">
                                <i class="fas fa-trash"></i>
                            </button>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>

    <!-- Attendance Records Section -->
    <div class="dashboard-section">
        <div class="section-header">
            <h2><i class="fas fa-clipboard-list"></i> Attendance Records</h2>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAttendanceModal">
                <i class="fas fa-plus"></i> Add Record
            </button>
        </div>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>User</th>
                        <th>Date</th>
                        <th>Time In</th>
                        <th>Time Out</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in attendance_records %}
                    <tr>
                        <td>{{ record.id }}</td>
                        <td>{{ record.full_name or record.username }}</td>
                        <td>{{ record.date }}</td>
                        <td>{{ record.time_in or 'N/A' }}</td>
                        <td>{{ record.time_out or 'N/A' }}</td>
                        <td>
                            <span class="badge bg-{{ 'success' if record.status == 'present' else 'warning' if record.status == 'late' else 'danger' }}">
                                {{ record.status.title() }}
                            </span>
                        </td>
                        <td>
                            <button class="btn btn-sm btn-outline-danger" onclick="deleteAttendance({{ record.id }})">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add New User</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ url_for('add_user') }}" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="newUsername" class="form-label">Username</label>
                        <input type="text" class="form-control" id="newUsername" name="username" required>
                    </div>
                    <div class="mb-3">
                        <label for="newFullName" class="form-label">Full Name</label>
                        <input type="text" class="form-control" id="newFullName" name="full_name" required>
                    </div>
                    <div class="mb-3">
                        <label for="newEmail" class="form-label">Email</label>
                        <input type="email" class="form-control" id="newEmail" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">Password</label>
                        <input type="password" class="form-control" id="newPassword" name="password" required>
                    </div>
                    <div class="mb-3">
                        <label for="newRole" class="form-label">Role</label>
                        <select class="form-control" id="newRole" name="role" required>
                            <option value="user">User</option>
                            <option value="admin">Admin</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add User</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Add Attendance Modal -->
<div class="modal fade" id="addAttendanceModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Attendance Record</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="{{ url_for('add_attendance') }}" method="POST">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="attendanceUser" class="form-label">User</label>
                        <select class="form-control" id="attendanceUser" name="user_id" required>
                            {% for user in users %}
                            <option value="{{ user.id }}">{{ user.full_name }} ({{ user.username }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="attendanceDate" class="form-label">Date</label>
                        <input type="date" class="form-control" id="attendanceDate" name="date" value="{{ moment().format('YYYY-MM-DD') }}" required>
                    </div>
                    <div class="mb-3">
                        <label for="timeIn" class="form-label">Time In</label>
                        <input type="time" class="form-control" id="timeIn" name="time_in" required>
                    </div>
                    <div class="mb-3">
                        <label for="timeOut" class="form-label">Time Out</label>
                        <input type="time" class="form-control" id="timeOut" name="time_out">
                    </div>
                    <div class="mb-3">
                        <label for="attendanceStatus" class="form-label">Status</label>
                        <select class="form-control" id="attendanceStatus" name="status" required>
                            <option value="present">Present</option>
                            <option value="late">Late</option>
                            <option value="absent">Absent</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Record</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}
