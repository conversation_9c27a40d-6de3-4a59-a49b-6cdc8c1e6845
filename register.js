// Registration functionality
document.addEventListener('DOMContentLoaded', function() {
    const registerForm = document.getElementById('registerForm');

    // Check if user is already logged in
    const currentUser = localStorage.getItem('currentUser');
    if (currentUser) {
        const user = JSON.parse(currentUser);
        if (user.role === 'admin') {
            window.location.href = 'admin.html';
        } else {
            window.location.href = 'user-dashboard.html';
        }
    }

    // Handle registration form submission
    registerForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const fullName = document.getElementById('fullName').value.trim();
        const username = document.getElementById('username').value.trim();
        const email = document.getElementById('email').value.trim();
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirmPassword').value;
        const agreeTerms = document.getElementById('agreeTerms').checked;

        // Clear previous error messages
        hideError();

        // Validate inputs
        if (!fullName || !username || !email || !password || !confirmPassword) {
            showError('Please fill in all fields');
            return;
        }

        if (!agreeTerms) {
            showError('Please agree to the Terms and Conditions');
            return;
        }

        if (password !== confirmPassword) {
            showError('Passwords do not match');
            return;
        }

        if (password.length < 6) {
            showError('Password must be at least 6 characters long');
            return;
        }

        if (!isValidEmail(email)) {
            showError('Please enter a valid email address');
            return;
        }

        if (username.length < 3) {
            showError('Username must be at least 3 characters long');
            return;
        }

        try {
            // Create new user
            const userData = {
                fullName: fullName,
                username: username,
                email: email,
                password: password,
                role: 'user'
            };

            const newUser = db.createUser(userData);
            
            // Show success message
            showSuccess('Account created successfully! Redirecting to login...');

            // Clear form
            registerForm.reset();

            // Redirect to login page after 2 seconds
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 2000);

        } catch (error) {
            showError(error.message);
            console.error('Registration error:', error);
        }
    });
});

// Toggle password visibility
function togglePassword(fieldId) {
    const passwordInput = document.getElementById(fieldId);
    const iconId = fieldId === 'password' ? 'passwordIcon' : 'confirmPasswordIcon';
    const passwordIcon = document.getElementById(iconId);
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        passwordIcon.className = 'fas fa-eye-slash';
    } else {
        passwordInput.type = 'password';
        passwordIcon.className = 'fas fa-eye';
    }
}

// Validate email format
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Show error message
function showError(message) {
    const errorDiv = document.getElementById('errorMessage');
    errorDiv.textContent = message;
    errorDiv.style.background = '#fee';
    errorDiv.style.borderColor = '#fcc';
    errorDiv.style.color = '#c33';
    errorDiv.className = 'error-message show';
}

// Hide error message
function hideError() {
    const errorDiv = document.getElementById('errorMessage');
    errorDiv.className = 'error-message';
}

// Show success message
function showSuccess(message) {
    const errorDiv = document.getElementById('errorMessage');
    errorDiv.textContent = message;
    errorDiv.style.background = '#d4edda';
    errorDiv.style.borderColor = '#c3e6cb';
    errorDiv.style.color = '#155724';
    errorDiv.className = 'error-message show';
}

// Real-time validation
document.addEventListener('DOMContentLoaded', function() {
    const usernameInput = document.getElementById('username');
    const passwordInput = document.getElementById('password');
    const confirmPasswordInput = document.getElementById('confirmPassword');

    // Username validation
    usernameInput.addEventListener('blur', function() {
        const username = this.value.trim();
        if (username.length > 0 && username.length < 3) {
            showError('Username must be at least 3 characters long');
        } else if (username.length >= 3) {
            // Check if username already exists
            const existingUser = db.getUserByUsername(username);
            if (existingUser) {
                showError('Username already exists');
            } else {
                hideError();
            }
        }
    });

    // Password strength validation
    passwordInput.addEventListener('input', function() {
        const password = this.value;
        if (password.length > 0 && password.length < 6) {
            showError('Password must be at least 6 characters long');
        } else if (password.length >= 6) {
            hideError();
        }
    });

    // Confirm password validation
    confirmPasswordInput.addEventListener('input', function() {
        const password = passwordInput.value;
        const confirmPassword = this.value;
        
        if (confirmPassword.length > 0 && password !== confirmPassword) {
            showError('Passwords do not match');
        } else if (password === confirmPassword && password.length >= 6) {
            hideError();
        }
    });
});
