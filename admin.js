// Admin panel functionality
document.addEventListener('DOMContentLoaded', function() {
    // Check admin authentication
    const currentUser = checkAdminAuth();
    if (!currentUser) return;

    // Display admin name
    document.getElementById('adminName').textContent = currentUser.fullName || currentUser.username;

    // Load dashboard data
    loadDashboard();

    // Set up form handlers
    setupFormHandlers();
});

function loadDashboard() {
    loadStatistics();
    loadUsersTable();
    loadAttendanceTable();
    populateUserDropdown();
}

function loadStatistics() {
    const stats = db.getStatistics();
    
    document.getElementById('totalUsers').textContent = stats.totalUsers;
    document.getElementById('totalAdmins').textContent = stats.totalAdmins;
    document.getElementById('todayAttendance').textContent = stats.todayAttendance;
    document.getElementById('totalRecords').textContent = stats.totalAttendanceRecords;
}

function loadUsersTable() {
    const users = db.getAllUsers();
    const tbody = document.getElementById('usersTableBody');
    
    tbody.innerHTML = '';
    
    users.forEach(user => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${user.id}</td>
            <td>${user.username}</td>
            <td>${user.fullName || 'N/A'}</td>
            <td>${user.email}</td>
            <td><span class="role-badge ${user.role}">${user.role}</span></td>
            <td>${new Date(user.created_at).toLocaleDateString()}</td>
            <td>
                <button class="action-btn edit-btn" onclick="editUser(${user.id})">
                    <i class="fas fa-edit"></i> Edit
                </button>
                ${user.id !== 1 ? `<button class="action-btn delete-btn" onclick="deleteUser(${user.id})">
                    <i class="fas fa-trash"></i> Delete
                </button>` : ''}
            </td>
        `;
        tbody.appendChild(row);
    });
}

function loadAttendanceTable() {
    const attendance = db.getAttendanceWithUserDetails();
    const tbody = document.getElementById('attendanceTableBody');
    
    tbody.innerHTML = '';
    
    attendance.forEach(record => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${record.id}</td>
            <td>${record.fullName || record.username}</td>
            <td>${record.date}</td>
            <td>${record.time_in || 'N/A'}</td>
            <td>${record.time_out || 'N/A'}</td>
            <td><span class="status-badge ${record.status}">${record.status}</span></td>
            <td>
                <button class="action-btn edit-btn" onclick="editAttendance(${record.id})">
                    <i class="fas fa-edit"></i> Edit
                </button>
                <button class="action-btn delete-btn" onclick="deleteAttendance(${record.id})">
                    <i class="fas fa-trash"></i> Delete
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function populateUserDropdown() {
    const users = db.getAllUsers();
    const select = document.getElementById('attendanceUser');
    
    select.innerHTML = '';
    
    users.forEach(user => {
        const option = document.createElement('option');
        option.value = user.id;
        option.textContent = `${user.fullName || user.username} (${user.username})`;
        select.appendChild(option);
    });
}

function setupFormHandlers() {
    // Add User Form
    document.getElementById('addUserForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const userData = {
            username: document.getElementById('newUsername').value.trim(),
            fullName: document.getElementById('newFullName').value.trim(),
            email: document.getElementById('newEmail').value.trim(),
            password: document.getElementById('newPassword').value,
            role: document.getElementById('newRole').value
        };

        try {
            db.createUser(userData);
            closeModal('addUserModal');
            loadDashboard();
            showNotification('User added successfully!', 'success');
            document.getElementById('addUserForm').reset();
        } catch (error) {
            showNotification(error.message, 'error');
        }
    });

    // Add Attendance Form
    document.getElementById('addAttendanceForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const attendanceData = {
            user_id: parseInt(document.getElementById('attendanceUser').value),
            date: document.getElementById('attendanceDate').value,
            time_in: document.getElementById('timeIn').value,
            time_out: document.getElementById('timeOut').value || null,
            status: document.getElementById('attendanceStatus').value
        };

        try {
            db.createAttendanceRecord(attendanceData);
            closeModal('addAttendanceModal');
            loadDashboard();
            showNotification('Attendance record added successfully!', 'success');
            document.getElementById('addAttendanceForm').reset();
        } catch (error) {
            showNotification(error.message, 'error');
        }
    });

    // Set default date to today
    document.getElementById('attendanceDate').value = new Date().toISOString().split('T')[0];
}

// Modal functions
function showAddUserModal() {
    document.getElementById('addUserModal').style.display = 'flex';
}

function showAddAttendanceModal() {
    document.getElementById('addAttendanceModal').style.display = 'flex';
}

function closeModal(modalId) {
    document.getElementById(modalId).style.display = 'none';
}

// User management functions
function editUser(userId) {
    const user = db.getUserById(userId);
    if (!user) return;

    const newUsername = prompt('Enter new username:', user.username);
    if (newUsername && newUsername !== user.username) {
        try {
            db.updateUser(userId, { username: newUsername });
            loadDashboard();
            showNotification('User updated successfully!', 'success');
        } catch (error) {
            showNotification(error.message, 'error');
        }
    }
}

function deleteUser(userId) {
    if (userId === 1) {
        showNotification('Cannot delete the default admin user!', 'error');
        return;
    }

    if (confirm('Are you sure you want to delete this user? This will also delete all their attendance records.')) {
        try {
            db.deleteUser(userId);
            loadDashboard();
            showNotification('User deleted successfully!', 'success');
        } catch (error) {
            showNotification(error.message, 'error');
        }
    }
}

// Attendance management functions
function editAttendance(recordId) {
    const record = db.getAllAttendance().find(r => r.id === recordId);
    if (!record) return;

    const newTimeOut = prompt('Enter new time out (HH:MM format):', record.time_out || '');
    if (newTimeOut !== null) {
        try {
            db.updateAttendanceRecord(recordId, { time_out: newTimeOut || null });
            loadDashboard();
            showNotification('Attendance record updated successfully!', 'success');
        } catch (error) {
            showNotification(error.message, 'error');
        }
    }
}

function deleteAttendance(recordId) {
    if (confirm('Are you sure you want to delete this attendance record?')) {
        try {
            db.deleteAttendanceRecord(recordId);
            loadDashboard();
            showNotification('Attendance record deleted successfully!', 'success');
        } catch (error) {
            showNotification(error.message, 'error');
        }
    }
}

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}
