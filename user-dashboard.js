// User dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    // Check user authentication
    const currentUser = checkAuth();
    if (!currentUser) return;

    // Display user name
    document.getElementById('userName').textContent = currentUser.fullName || currentUser.username;

    // Load dashboard data
    loadUserDashboard();

    // Update time every second
    updateCurrentTime();
    setInterval(updateCurrentTime, 1000);

    // Check today's attendance status
    checkTodayAttendanceStatus();
});

function loadUserDashboard() {
    const currentUser = JSON.parse(localStorage.getItem('currentUser'));
    const userAttendance = db.getAttendanceByUserId(currentUser.id);
    
    // Update statistics
    document.getElementById('userAttendanceCount').textContent = userAttendance.length;
    
    // Calculate attendance rate (assuming 30 days for demo)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentAttendance = userAttendance.filter(record => 
        new Date(record.date) >= thirtyDaysAgo
    );
    
    const attendanceRate = recentAttendance.length > 0 ? 
        Math.round((recentAttendance.filter(r => r.status === 'present').length / recentAttendance.length) * 100) : 0;
    
    document.getElementById('attendanceRate').textContent = attendanceRate + '%';

    // Load attendance table
    loadUserAttendanceTable();
}

function loadUserAttendanceTable() {
    const currentUser = JSON.parse(localStorage.getItem('currentUser'));
    const userAttendance = db.getAttendanceByUserId(currentUser.id);
    const tbody = document.getElementById('userAttendanceTableBody');
    
    tbody.innerHTML = '';
    
    // Sort by date (newest first)
    userAttendance.sort((a, b) => new Date(b.date) - new Date(a.date));
    
    userAttendance.forEach(record => {
        const row = document.createElement('tr');
        const hoursWorked = calculateHoursWorked(record.time_in, record.time_out);
        
        row.innerHTML = `
            <td>${new Date(record.date).toLocaleDateString()}</td>
            <td>${record.time_in || 'N/A'}</td>
            <td>${record.time_out || 'N/A'}</td>
            <td>${hoursWorked}</td>
            <td><span class="status-badge ${record.status}">${record.status}</span></td>
        `;
        tbody.appendChild(row);
    });
}

function updateCurrentTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString();
    const dateString = now.toLocaleDateString();
    
    document.getElementById('currentTime').textContent = timeString;
    document.getElementById('todayDate').textContent = dateString;
}

function checkTodayAttendanceStatus() {
    const currentUser = JSON.parse(localStorage.getItem('currentUser'));
    const today = new Date().toISOString().split('T')[0];
    const todayAttendance = db.getAttendanceByUserId(currentUser.id)
        .find(record => record.date === today);
    
    const checkInBtn = document.getElementById('checkInBtn');
    const checkOutBtn = document.getElementById('checkOutBtn');
    const statusDiv = document.getElementById('checkInStatus');
    
    if (todayAttendance) {
        if (todayAttendance.time_out) {
            // Already checked out
            checkInBtn.disabled = true;
            checkOutBtn.disabled = true;
            statusDiv.innerHTML = `
                <div style="color: #28a745;">
                    <i class="fas fa-check-circle"></i> 
                    Checked in at ${todayAttendance.time_in}, checked out at ${todayAttendance.time_out}
                </div>
            `;
        } else {
            // Checked in but not out
            checkInBtn.disabled = true;
            checkOutBtn.disabled = false;
            checkOutBtn.style.background = '#28a745';
            statusDiv.innerHTML = `
                <div style="color: #667eea;">
                    <i class="fas fa-clock"></i> 
                    Checked in at ${todayAttendance.time_in}. Don't forget to check out!
                </div>
            `;
        }
    } else {
        // Not checked in yet
        checkInBtn.disabled = false;
        checkOutBtn.disabled = true;
        statusDiv.innerHTML = `
            <div style="color: #666;">
                <i class="fas fa-info-circle"></i> 
                You haven't checked in today yet.
            </div>
        `;
    }
}

function checkIn() {
    const currentUser = JSON.parse(localStorage.getItem('currentUser'));
    const today = new Date().toISOString().split('T')[0];
    const currentTime = new Date().toTimeString().split(' ')[0].substring(0, 5);
    
    // Check if already checked in today
    const existingRecord = db.getAttendanceByUserId(currentUser.id)
        .find(record => record.date === today);
    
    if (existingRecord) {
        showNotification('You have already checked in today!', 'error');
        return;
    }
    
    try {
        const attendanceData = {
            user_id: currentUser.id,
            date: today,
            time_in: currentTime,
            time_out: null,
            status: 'present'
        };
        
        db.createAttendanceRecord(attendanceData);
        showNotification('Checked in successfully!', 'success');
        
        // Refresh dashboard
        loadUserDashboard();
        checkTodayAttendanceStatus();
        
    } catch (error) {
        showNotification('Error checking in. Please try again.', 'error');
        console.error('Check-in error:', error);
    }
}

function checkOut() {
    const currentUser = JSON.parse(localStorage.getItem('currentUser'));
    const today = new Date().toISOString().split('T')[0];
    const currentTime = new Date().toTimeString().split(' ')[0].substring(0, 5);
    
    // Find today's attendance record
    const todayRecord = db.getAttendanceByUserId(currentUser.id)
        .find(record => record.date === today);
    
    if (!todayRecord) {
        showNotification('You need to check in first!', 'error');
        return;
    }
    
    if (todayRecord.time_out) {
        showNotification('You have already checked out today!', 'error');
        return;
    }
    
    try {
        db.updateAttendanceRecord(todayRecord.id, { time_out: currentTime });
        showNotification('Checked out successfully!', 'success');
        
        // Refresh dashboard
        loadUserDashboard();
        checkTodayAttendanceStatus();
        
    } catch (error) {
        showNotification('Error checking out. Please try again.', 'error');
        console.error('Check-out error:', error);
    }
}

function calculateHoursWorked(timeIn, timeOut) {
    if (!timeIn || !timeOut) return 'N/A';
    
    const [inHours, inMinutes] = timeIn.split(':').map(Number);
    const [outHours, outMinutes] = timeOut.split(':').map(Number);
    
    const inTotalMinutes = inHours * 60 + inMinutes;
    const outTotalMinutes = outHours * 60 + outMinutes;
    
    const diffMinutes = outTotalMinutes - inTotalMinutes;
    
    if (diffMinutes < 0) return 'Invalid';
    
    const hours = Math.floor(diffMinutes / 60);
    const minutes = diffMinutes % 60;
    
    return `${hours}h ${minutes}m`;
}

// Notification system (reuse from admin.js)
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.textContent = message;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.classList.add('show');
    }, 100);
    
    setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, 3000);
}
