<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - Attendance System</title>
    <link rel="stylesheet" href="login.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="admin-container">
        <!-- Header -->
        <div class="admin-header">
            <div>
                <h1><i class="fas fa-tachometer-alt"></i> Admin Dashboard</h1>
                <p>Welcome, <span id="adminName">Administrator</span></p>
            </div>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i> Logout
            </button>
        </div>

        <!-- Statistics -->
        <div class="stats-grid">
            <div class="stat-card">
                <i class="fas fa-users"></i>
                <h3 id="totalUsers">0</h3>
                <p>Total Users</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-user-shield"></i>
                <h3 id="totalAdmins">0</h3>
                <p>Administrators</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-calendar-check"></i>
                <h3 id="todayAttendance">0</h3>
                <p>Today's Attendance</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-chart-line"></i>
                <h3 id="totalRecords">0</h3>
                <p>Total Records</p>
            </div>
        </div>

        <!-- User Management Section -->
        <div class="admin-section">
            <div class="section-header">
                <h2><i class="fas fa-users-cog"></i> User Management</h2>
                <button class="add-btn" onclick="showAddUserModal()">
                    <i class="fas fa-plus"></i> Add User
                </button>
            </div>
            <div class="table-container">
                <table id="usersTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Username</th>
                            <th>Full Name</th>
                            <th>Email</th>
                            <th>Role</th>
                            <th>Created</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="usersTableBody">
                        <!-- Users will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Attendance Records Section -->
        <div class="admin-section">
            <div class="section-header">
                <h2><i class="fas fa-clipboard-list"></i> Attendance Records</h2>
                <button class="add-btn" onclick="showAddAttendanceModal()">
                    <i class="fas fa-plus"></i> Add Record
                </button>
            </div>
            <div class="table-container">
                <table id="attendanceTable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>User</th>
                            <th>Date</th>
                            <th>Time In</th>
                            <th>Time Out</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="attendanceTableBody">
                        <!-- Attendance records will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Add User Modal -->
    <div id="addUserModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add New User</h3>
                <span class="close" onclick="closeModal('addUserModal')">&times;</span>
            </div>
            <form id="addUserForm">
                <div class="form-group">
                    <label for="newUsername">Username:</label>
                    <input type="text" id="newUsername" required>
                </div>
                <div class="form-group">
                    <label for="newFullName">Full Name:</label>
                    <input type="text" id="newFullName" required>
                </div>
                <div class="form-group">
                    <label for="newEmail">Email:</label>
                    <input type="email" id="newEmail" required>
                </div>
                <div class="form-group">
                    <label for="newPassword">Password:</label>
                    <input type="password" id="newPassword" required>
                </div>
                <div class="form-group">
                    <label for="newRole">Role:</label>
                    <select id="newRole" required>
                        <option value="user">User</option>
                        <option value="admin">Admin</option>
                    </select>
                </div>
                <div class="modal-actions">
                    <button type="button" onclick="closeModal('addUserModal')">Cancel</button>
                    <button type="submit">Add User</button>
                </div>
            </form>
        </div>
    </div>

    <!-- Add Attendance Modal -->
    <div id="addAttendanceModal" class="modal" style="display: none;">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Add Attendance Record</h3>
                <span class="close" onclick="closeModal('addAttendanceModal')">&times;</span>
            </div>
            <form id="addAttendanceForm">
                <div class="form-group">
                    <label for="attendanceUser">User:</label>
                    <select id="attendanceUser" required>
                        <!-- Users will be populated here -->
                    </select>
                </div>
                <div class="form-group">
                    <label for="attendanceDate">Date:</label>
                    <input type="date" id="attendanceDate" required>
                </div>
                <div class="form-group">
                    <label for="timeIn">Time In:</label>
                    <input type="time" id="timeIn" required>
                </div>
                <div class="form-group">
                    <label for="timeOut">Time Out:</label>
                    <input type="time" id="timeOut">
                </div>
                <div class="form-group">
                    <label for="attendanceStatus">Status:</label>
                    <select id="attendanceStatus" required>
                        <option value="present">Present</option>
                        <option value="late">Late</option>
                        <option value="absent">Absent</option>
                    </select>
                </div>
                <div class="modal-actions">
                    <button type="button" onclick="closeModal('addAttendanceModal')">Cancel</button>
                    <button type="submit">Add Record</button>
                </div>
            </form>
        </div>
    </div>

    <script src="database.js"></script>
    <script src="login.js"></script>
    <script src="admin.js"></script>
</body>
</html>
