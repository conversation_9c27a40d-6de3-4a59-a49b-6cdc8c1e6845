{% extends "base.html" %}

{% block title %}User Dashboard - Attendance System{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Header -->
    <div class="dashboard-header">
        <div>
            <h1><i class="fas fa-user"></i> User Dashboard</h1>
            <p>Welcome, {{ session.full_name }}</p>
        </div>
        <a href="{{ url_for('logout') }}" class="btn btn-danger">
            <i class="fas fa-sign-out-alt"></i> Logout
        </a>
    </div>

    <!-- Quick Actions -->
    <div class="stats-grid">
        <div class="stat-card">
            <i class="fas fa-clock"></i>
            <h3 id="currentTime">--:--</h3>
            <p>Current Time</p>
        </div>
        <div class="stat-card">
            <i class="fas fa-calendar-day"></i>
            <h3 id="todayDate">Today</h3>
            <p>Today's Date</p>
        </div>
        <div class="stat-card">
            <i class="fas fa-chart-line"></i>
            <h3>{{ attendance_records|length }}</h3>
            <p>Total Records</p>
        </div>
        <div class="stat-card">
            <i class="fas fa-percentage"></i>
            <h3>{{ ((attendance_records|selectattr('status', 'equalto', 'present')|list|length / attendance_records|length * 100) if attendance_records else 0)|round }}%</h3>
            <p>Attendance Rate</p>
        </div>
    </div>

    <!-- Quick Check-in/out -->
    <div class="dashboard-section">
        <div class="section-header">
            <h2><i class="fas fa-stopwatch"></i> Quick Actions</h2>
        </div>
        <div class="text-center p-4">
            {% if today_record %}
                {% if today_record.time_out %}
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle"></i>
                        You checked in at {{ today_record.time_in }} and checked out at {{ today_record.time_out }} today.
                    </div>
                    <button class="btn btn-secondary" disabled>
                        <i class="fas fa-check"></i> Already Checked Out
                    </button>
                {% else %}
                    <div class="alert alert-info">
                        <i class="fas fa-clock"></i>
                        You checked in at {{ today_record.time_in }}. Don't forget to check out!
                    </div>
                    <form action="{{ url_for('check_out') }}" method="POST" style="display: inline;">
                        <button type="submit" class="btn btn-success btn-lg">
                            <i class="fas fa-sign-out-alt"></i> Check Out
                        </button>
                    </form>
                {% endif %}
            {% else %}
                <div class="alert alert-warning">
                    <i class="fas fa-info-circle"></i>
                    You haven't checked in today yet.
                </div>
                <form action="{{ url_for('check_in') }}" method="POST" style="display: inline;">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="fas fa-sign-in-alt"></i> Check In
                    </button>
                </form>
            {% endif %}
        </div>
    </div>

    <!-- My Attendance Records -->
    <div class="dashboard-section">
        <div class="section-header">
            <h2><i class="fas fa-history"></i> My Attendance History</h2>
        </div>
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Date</th>
                        <th>Time In</th>
                        <th>Time Out</th>
                        <th>Hours Worked</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    {% for record in attendance_records %}
                    <tr>
                        <td>{{ record.date }}</td>
                        <td>{{ record.time_in or 'N/A' }}</td>
                        <td>{{ record.time_out or 'N/A' }}</td>
                        <td>
                            {% if record.time_in and record.time_out %}
                                {% set time_in_parts = record.time_in.split(':') %}
                                {% set time_out_parts = record.time_out.split(':') %}
                                {% set in_minutes = (time_in_parts[0]|int * 60) + time_in_parts[1]|int %}
                                {% set out_minutes = (time_out_parts[0]|int * 60) + time_out_parts[1]|int %}
                                {% set diff_minutes = out_minutes - in_minutes %}
                                {% if diff_minutes >= 0 %}
                                    {{ (diff_minutes // 60) }}h {{ (diff_minutes % 60) }}m
                                {% else %}
                                    Invalid
                                {% endif %}
                            {% else %}
                                N/A
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-{{ 'success' if record.status == 'present' else 'warning' if record.status == 'late' else 'danger' }}">
                                {{ record.status.title() }}
                            </span>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<script>
// Update current time
function updateTime() {
    const now = new Date();
    document.getElementById('currentTime').textContent = now.toLocaleTimeString();
    document.getElementById('todayDate').textContent = now.toLocaleDateString();
}

updateTime();
setInterval(updateTime, 1000);
</script>
{% endblock %}
