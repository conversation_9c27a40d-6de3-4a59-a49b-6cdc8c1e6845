from flask import Flask, render_template, request, redirect, url_for, session, flash, jsonify
import sqlite3
import hashlib
from datetime import datetime, date
import os

app = Flask(__name__)
app.secret_key = 'your-secret-key-change-this-in-production'

# Database configuration
DATABASE = 'attendance.db'

def get_db_connection():
    """Get database connection"""
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

def init_database():
    """Initialize database with tables and default admin user"""
    conn = get_db_connection()
    
    # Create users table
    conn.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            email TEXT NOT NULL,
            full_name TEXT NOT NULL,
            role TEXT NOT NULL DEFAULT 'user',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Create attendance table
    conn.execute('''
        CREATE TABLE IF NOT EXISTS attendance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            user_id INTEGER NOT NULL,
            date DATE NOT NULL,
            time_in TIME,
            time_out TIME,
            status TEXT DEFAULT 'present',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (user_id) REFERENCES users (id)
        )
    ''')
    
    # Check if admin user exists
    admin = conn.execute('SELECT * FROM users WHERE username = ?', ('admin',)).fetchone()
    if not admin:
        # Create default admin user
        admin_password = hashlib.sha256('admin123'.encode()).hexdigest()
        conn.execute('''
            INSERT INTO users (username, password, email, full_name, role)
            VALUES (?, ?, ?, ?, ?)
        ''', ('admin', admin_password, '<EMAIL>', 'System Administrator', 'admin'))
    
    conn.commit()
    conn.close()

def hash_password(password):
    """Hash password using SHA256"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password, hashed):
    """Verify password against hash"""
    return hashlib.sha256(password.encode()).hexdigest() == hashed

@app.route('/')
def index():
    """Home page - redirect to login or dashboard based on session"""
    if 'user_id' in session:
        if session.get('role') == 'admin':
            return redirect(url_for('admin_dashboard'))
        else:
            return redirect(url_for('user_dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Login page"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        
        conn = get_db_connection()
        user = conn.execute(
            'SELECT * FROM users WHERE username = ?', (username,)
        ).fetchone()
        conn.close()
        
        if user and verify_password(password, user['password']):
            session['user_id'] = user['id']
            session['username'] = user['username']
            session['full_name'] = user['full_name']
            session['role'] = user['role']
            
            flash('Login successful!', 'success')
            
            if user['role'] == 'admin':
                return redirect(url_for('admin_dashboard'))
            else:
                return redirect(url_for('user_dashboard'))
        else:
            flash('Invalid username or password!', 'error')
    
    return render_template('login.html')

@app.route('/register', methods=['GET', 'POST'])
def register():
    """Registration page"""
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        confirm_password = request.form['confirm_password']
        email = request.form['email']
        full_name = request.form['full_name']
        
        # Validation
        if password != confirm_password:
            flash('Passwords do not match!', 'error')
            return render_template('register.html')
        
        if len(password) < 6:
            flash('Password must be at least 6 characters long!', 'error')
            return render_template('register.html')
        
        conn = get_db_connection()
        
        # Check if username already exists
        existing_user = conn.execute(
            'SELECT * FROM users WHERE username = ?', (username,)
        ).fetchone()
        
        if existing_user:
            flash('Username already exists!', 'error')
            conn.close()
            return render_template('register.html')
        
        # Create new user
        hashed_password = hash_password(password)
        try:
            conn.execute('''
                INSERT INTO users (username, password, email, full_name, role)
                VALUES (?, ?, ?, ?, ?)
            ''', (username, hashed_password, email, full_name, 'user'))
            conn.commit()
            conn.close()
            
            flash('Registration successful! Please login.', 'success')
            return redirect(url_for('login'))
        except sqlite3.Error as e:
            flash('Registration failed! Please try again.', 'error')
            conn.close()
    
    return render_template('register.html')

@app.route('/logout')
def logout():
    """Logout and clear session"""
    session.clear()
    flash('You have been logged out.', 'info')
    return redirect(url_for('login'))

@app.route('/admin')
def admin_dashboard():
    """Admin dashboard"""
    if 'user_id' not in session or session.get('role') != 'admin':
        flash('Access denied! Admin privileges required.', 'error')
        return redirect(url_for('login'))
    
    conn = get_db_connection()
    
    # Get statistics
    total_users = conn.execute('SELECT COUNT(*) as count FROM users').fetchone()['count']
    total_admins = conn.execute('SELECT COUNT(*) as count FROM users WHERE role = "admin"').fetchone()['count']
    total_records = conn.execute('SELECT COUNT(*) as count FROM attendance').fetchone()['count']
    today_attendance = conn.execute(
        'SELECT COUNT(*) as count FROM attendance WHERE date = ?', (date.today(),)
    ).fetchone()['count']
    
    # Get all users
    users = conn.execute('''
        SELECT id, username, email, full_name, role, created_at 
        FROM users ORDER BY created_at DESC
    ''').fetchall()
    
    # Get attendance records with user details
    attendance_records = conn.execute('''
        SELECT a.*, u.username, u.full_name 
        FROM attendance a 
        JOIN users u ON a.user_id = u.id 
        ORDER BY a.date DESC, a.time_in DESC
    ''').fetchall()
    
    conn.close()
    
    stats = {
        'total_users': total_users,
        'total_admins': total_admins,
        'total_records': total_records,
        'today_attendance': today_attendance
    }
    
    return render_template('admin_dashboard.html', 
                         stats=stats, 
                         users=users, 
                         attendance_records=attendance_records)

@app.route('/user')
def user_dashboard():
    """User dashboard"""
    if 'user_id' not in session:
        flash('Please login to access your dashboard.', 'error')
        return redirect(url_for('login'))

    user_id = session['user_id']
    conn = get_db_connection()

    # Get user's attendance records
    user_attendance = conn.execute('''
        SELECT * FROM attendance
        WHERE user_id = ?
        ORDER BY date DESC
    ''', (user_id,)).fetchall()

    # Check today's attendance
    today_record = conn.execute('''
        SELECT * FROM attendance
        WHERE user_id = ? AND date = ?
    ''', (user_id, date.today())).fetchone()

    conn.close()

    return render_template('user_dashboard.html',
                         attendance_records=user_attendance,
                         today_record=today_record)

@app.route('/check_in', methods=['POST'])
def check_in():
    """Check in user for today"""
    if 'user_id' not in session:
        flash('Please login first.', 'error')
        return redirect(url_for('login'))

    user_id = session['user_id']
    today = date.today()
    current_time = datetime.now().strftime('%H:%M')

    conn = get_db_connection()

    # Check if already checked in today
    existing_record = conn.execute('''
        SELECT * FROM attendance WHERE user_id = ? AND date = ?
    ''', (user_id, today)).fetchone()

    if existing_record:
        flash('You have already checked in today!', 'error')
    else:
        # Create new attendance record
        conn.execute('''
            INSERT INTO attendance (user_id, date, time_in, status)
            VALUES (?, ?, ?, ?)
        ''', (user_id, today, current_time, 'present'))
        conn.commit()
        flash('Checked in successfully!', 'success')

    conn.close()
    return redirect(url_for('user_dashboard'))

@app.route('/check_out', methods=['POST'])
def check_out():
    """Check out user for today"""
    if 'user_id' not in session:
        flash('Please login first.', 'error')
        return redirect(url_for('login'))

    user_id = session['user_id']
    today = date.today()
    current_time = datetime.now().strftime('%H:%M')

    conn = get_db_connection()

    # Find today's attendance record
    today_record = conn.execute('''
        SELECT * FROM attendance WHERE user_id = ? AND date = ?
    ''', (user_id, today)).fetchone()

    if not today_record:
        flash('You need to check in first!', 'error')
    elif today_record['time_out']:
        flash('You have already checked out today!', 'error')
    else:
        # Update with check out time
        conn.execute('''
            UPDATE attendance SET time_out = ? WHERE id = ?
        ''', (current_time, today_record['id']))
        conn.commit()
        flash('Checked out successfully!', 'success')

    conn.close()
    return redirect(url_for('user_dashboard'))

@app.route('/add_user', methods=['POST'])
def add_user():
    """Add new user (admin only)"""
    if 'user_id' not in session or session.get('role') != 'admin':
        flash('Access denied!', 'error')
        return redirect(url_for('login'))

    username = request.form['username']
    full_name = request.form['full_name']
    email = request.form['email']
    password = request.form['password']
    role = request.form['role']

    conn = get_db_connection()

    # Check if username already exists
    existing_user = conn.execute(
        'SELECT * FROM users WHERE username = ?', (username,)
    ).fetchone()

    if existing_user:
        flash('Username already exists!', 'error')
    else:
        # Create new user
        hashed_password = hash_password(password)
        try:
            conn.execute('''
                INSERT INTO users (username, password, email, full_name, role)
                VALUES (?, ?, ?, ?, ?)
            ''', (username, hashed_password, email, full_name, role))
            conn.commit()
            flash('User added successfully!', 'success')
        except sqlite3.Error as e:
            flash('Error adding user!', 'error')

    conn.close()
    return redirect(url_for('admin_dashboard'))

@app.route('/add_attendance', methods=['POST'])
def add_attendance():
    """Add attendance record (admin only)"""
    if 'user_id' not in session or session.get('role') != 'admin':
        flash('Access denied!', 'error')
        return redirect(url_for('login'))

    user_id = request.form['user_id']
    attendance_date = request.form['date']
    time_in = request.form['time_in']
    time_out = request.form['time_out'] if request.form['time_out'] else None
    status = request.form['status']

    conn = get_db_connection()

    try:
        conn.execute('''
            INSERT INTO attendance (user_id, date, time_in, time_out, status)
            VALUES (?, ?, ?, ?, ?)
        ''', (user_id, attendance_date, time_in, time_out, status))
        conn.commit()
        flash('Attendance record added successfully!', 'success')
    except sqlite3.Error as e:
        flash('Error adding attendance record!', 'error')

    conn.close()
    return redirect(url_for('admin_dashboard'))

@app.route('/delete_user', methods=['POST'])
def delete_user():
    """Delete user (admin only)"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({'success': False, 'message': 'Access denied'})

    data = request.get_json()
    user_id = data.get('user_id')

    if user_id == 1:
        return jsonify({'success': False, 'message': 'Cannot delete default admin user'})

    conn = get_db_connection()

    try:
        # Delete user's attendance records first
        conn.execute('DELETE FROM attendance WHERE user_id = ?', (user_id,))
        # Delete user
        conn.execute('DELETE FROM users WHERE id = ?', (user_id,))
        conn.commit()
        conn.close()
        return jsonify({'success': True})
    except sqlite3.Error as e:
        conn.close()
        return jsonify({'success': False, 'message': str(e)})

@app.route('/delete_attendance', methods=['POST'])
def delete_attendance():
    """Delete attendance record (admin only)"""
    if 'user_id' not in session or session.get('role') != 'admin':
        return jsonify({'success': False, 'message': 'Access denied'})

    data = request.get_json()
    record_id = data.get('record_id')

    conn = get_db_connection()

    try:
        conn.execute('DELETE FROM attendance WHERE id = ?', (record_id,))
        conn.commit()
        conn.close()
        return jsonify({'success': True})
    except sqlite3.Error as e:
        conn.close()
        return jsonify({'success': False, 'message': str(e)})

if __name__ == '__main__':
    init_database()
    app.run(debug=True)
