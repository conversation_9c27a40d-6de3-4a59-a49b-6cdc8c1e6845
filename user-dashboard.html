<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>User Dashboard - Attendance System</title>
    <link rel="stylesheet" href="login.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="admin-container">
        <!-- Header -->
        <div class="admin-header">
            <div>
                <h1><i class="fas fa-user"></i> User Dashboard</h1>
                <p>Welcome, <span id="userName">User</span></p>
            </div>
            <button class="logout-btn" onclick="logout()">
                <i class="fas fa-sign-out-alt"></i> Logout
            </button>
        </div>

        <!-- Quick Actions -->
        <div class="stats-grid">
            <div class="stat-card">
                <i class="fas fa-clock"></i>
                <h3 id="currentTime">--:--</h3>
                <p>Current Time</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-calendar-day"></i>
                <h3 id="todayDate">Today</h3>
                <p>Today's Date</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-chart-line"></i>
                <h3 id="userAttendanceCount">0</h3>
                <p>Total Records</p>
            </div>
            <div class="stat-card">
                <i class="fas fa-percentage"></i>
                <h3 id="attendanceRate">0%</h3>
                <p>Attendance Rate</p>
            </div>
        </div>

        <!-- Quick Check-in/out -->
        <div class="admin-section">
            <div class="section-header">
                <h2><i class="fas fa-stopwatch"></i> Quick Actions</h2>
            </div>
            <div style="padding: 20px; text-align: center;">
                <button id="checkInBtn" class="login-btn" style="margin: 10px; width: auto;" onclick="checkIn()">
                    <i class="fas fa-sign-in-alt"></i> Check In
                </button>
                <button id="checkOutBtn" class="login-btn" style="margin: 10px; width: auto; background: #dc3545;" onclick="checkOut()" disabled>
                    <i class="fas fa-sign-out-alt"></i> Check Out
                </button>
                <div id="checkInStatus" style="margin-top: 15px; font-weight: 500;"></div>
            </div>
        </div>

        <!-- My Attendance Records -->
        <div class="admin-section">
            <div class="section-header">
                <h2><i class="fas fa-history"></i> My Attendance History</h2>
            </div>
            <div class="table-container">
                <table id="userAttendanceTable">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Time In</th>
                            <th>Time Out</th>
                            <th>Hours Worked</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody id="userAttendanceTableBody">
                        <!-- User attendance records will be populated here -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="database.js"></script>
    <script src="login.js"></script>
    <script src="user-dashboard.js"></script>
</body>
</html>
