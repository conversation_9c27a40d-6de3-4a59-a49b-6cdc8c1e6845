// Database management using localStorage to simulate SQLite
class AttendanceDB {
    constructor() {
        this.initializeDatabase();
    }

    // Initialize database with default admin user
    initializeDatabase() {
        if (!localStorage.getItem('users')) {
            const defaultUsers = [
                {
                    id: 1,
                    username: 'admin',
                    password: 'admin123',
                    email: '<EMAIL>',
                    role: 'admin',
                    fullName: 'System Administrator',
                    created_at: new Date().toISOString()
                }
            ];
            localStorage.setItem('users', JSON.stringify(defaultUsers));
            localStorage.setItem('userIdCounter', '2');
        }

        if (!localStorage.getItem('attendance')) {
            localStorage.setItem('attendance', JSON.stringify([]));
            localStorage.setItem('attendanceIdCounter', '1');
        }
    }

    // User management methods
    getAllUsers() {
        return JSON.parse(localStorage.getItem('users') || '[]');
    }

    getUserById(id) {
        const users = this.getAllUsers();
        return users.find(user => user.id === parseInt(id));
    }

    getUserByUsername(username) {
        const users = this.getAllUsers();
        return users.find(user => user.username === username);
    }

    createUser(userData) {
        const users = this.getAllUsers();
        const counter = parseInt(localStorage.getItem('userIdCounter') || '1');
        
        // Check if username already exists
        if (this.getUserByUsername(userData.username)) {
            throw new Error('Username already exists');
        }

        const newUser = {
            id: counter,
            username: userData.username,
            password: userData.password,
            email: userData.email,
            role: userData.role || 'user',
            fullName: userData.fullName,
            created_at: new Date().toISOString()
        };

        users.push(newUser);
        localStorage.setItem('users', JSON.stringify(users));
        localStorage.setItem('userIdCounter', (counter + 1).toString());
        
        return newUser;
    }

    updateUser(id, userData) {
        const users = this.getAllUsers();
        const index = users.findIndex(user => user.id === parseInt(id));
        
        if (index === -1) {
            throw new Error('User not found');
        }

        users[index] = { ...users[index], ...userData };
        localStorage.setItem('users', JSON.stringify(users));
        return users[index];
    }

    deleteUser(id) {
        const users = this.getAllUsers();
        const filteredUsers = users.filter(user => user.id !== parseInt(id));
        localStorage.setItem('users', JSON.stringify(filteredUsers));
        
        // Also delete user's attendance records
        const attendance = this.getAllAttendance();
        const filteredAttendance = attendance.filter(record => record.user_id !== parseInt(id));
        localStorage.setItem('attendance', JSON.stringify(filteredAttendance));
    }

    // Authentication methods
    authenticateUser(username, password) {
        const user = this.getUserByUsername(username);
        if (user && user.password === password) {
            return user;
        }
        return null;
    }

    // Attendance management methods
    getAllAttendance() {
        return JSON.parse(localStorage.getItem('attendance') || '[]');
    }

    getAttendanceByUserId(userId) {
        const attendance = this.getAllAttendance();
        return attendance.filter(record => record.user_id === parseInt(userId));
    }

    createAttendanceRecord(attendanceData) {
        const attendance = this.getAllAttendance();
        const counter = parseInt(localStorage.getItem('attendanceIdCounter') || '1');

        const newRecord = {
            id: counter,
            user_id: attendanceData.user_id,
            date: attendanceData.date,
            time_in: attendanceData.time_in,
            time_out: attendanceData.time_out || null,
            status: attendanceData.status || 'present',
            created_at: new Date().toISOString()
        };

        attendance.push(newRecord);
        localStorage.setItem('attendance', JSON.stringify(attendance));
        localStorage.setItem('attendanceIdCounter', (counter + 1).toString());
        
        return newRecord;
    }

    updateAttendanceRecord(id, attendanceData) {
        const attendance = this.getAllAttendance();
        const index = attendance.findIndex(record => record.id === parseInt(id));
        
        if (index === -1) {
            throw new Error('Attendance record not found');
        }

        attendance[index] = { ...attendance[index], ...attendanceData };
        localStorage.setItem('attendance', JSON.stringify(attendance));
        return attendance[index];
    }

    deleteAttendanceRecord(id) {
        const attendance = this.getAllAttendance();
        const filteredAttendance = attendance.filter(record => record.id !== parseInt(id));
        localStorage.setItem('attendance', JSON.stringify(filteredAttendance));
    }

    // Utility methods
    getAttendanceWithUserDetails() {
        const attendance = this.getAllAttendance();
        const users = this.getAllUsers();
        
        return attendance.map(record => {
            const user = users.find(u => u.id === record.user_id);
            return {
                ...record,
                username: user ? user.username : 'Unknown',
                fullName: user ? user.fullName : 'Unknown User'
            };
        });
    }

    getStatistics() {
        const users = this.getAllUsers();
        const attendance = this.getAllAttendance();
        const today = new Date().toISOString().split('T')[0];
        
        return {
            totalUsers: users.length,
            totalAdmins: users.filter(u => u.role === 'admin').length,
            totalRegularUsers: users.filter(u => u.role === 'user').length,
            totalAttendanceRecords: attendance.length,
            todayAttendance: attendance.filter(a => a.date === today).length
        };
    }
}

// Initialize database instance
const db = new AttendanceDB();
